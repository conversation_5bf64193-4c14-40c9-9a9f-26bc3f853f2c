import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { PDFLoader } from "langchain/document_loaders/fs/pdf";
import { z } from "zod";
import { ChatOpenAI,OpenAIEmbeddings } from "@langchain/openai";

const zodSchema: any = z.object({
  first_name: z.string().optional(),
  middle_name: z.string().optional(),
  last_name: z.string().optional(),
  email: z.string().optional(),
  age: z.number().optional(),
  birth_date: z.date().optional(),
  gender: z.string().optional(),
  marital_status: z.string().optional(),
  contact: z.string().optional(),
  current_address: z
    .object({
      house_no: z.string().optional(),
      street: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().optional(),
      zip_code: z.string().optional(),
    })
    .optional(),
  permanent_address: z
    .object({
      house_no: z.string().optional(),
      street: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().optional(),
      zip_code: z.string().optional(),
    })
    .optional(),
  total_experience: z.number().optional(),
  language: z.array(z.string()).optional(),
  social_media: z
    .array(
      z.object({
        linked_in: z.string().optional(),
        facebook: z.string().optional(),
        twitter: z.string().optional(),
        instagram: z.string().optional(),
      })
    )
    .optional(),
  education_details: z
    .array(
      z.object({
        id: z.string().optional(),
        university_institute_name: z.string().optional(),
        course_name: z.string().optional(),
        specification: z.string().optional(),
        course_period: z
          .object({
            start_date: z.date().optional(),
            end_date: z.date().optional(),
          })
          .optional(),
        percentage: z.string().optional(),
      })
    )
    .optional(),
  skills: z
    .array(
      z.object({
        skill_name: z.string(),
        years_of_experience: z.number().optional(),
      })
    )
    .optional(),
  experience_details: z
    .array(
      z.object({
        company_name: z.string().optional(),
        postion: z.string().optional(),
        location: z.string().optional(),
        job_description: z.string().optional(),
        service_period: z
          .object({
            start_date: z.date().optional(),
            end_date: z.date().optional(),
          })
          .optional(),
        skills: z.array(z.string().optional()).optional(),
      })
    )
    .optional(),
  certificates: z
    .array(
      z.object({
        certificate_name: z.string().optional(),
        institute_name: z.string().optional(),
        certificate_period: z
          .object({
            start_date: z.date().optional(),
            end_date: z.date().optional(),
          })
          .optional(),
        expiring_date: z.date().optional(),
      })
    )
    .optional(),
  awards: z
    .array(
      z.object({
        award_name: z.string().optional(),
        company_name: z.string().optional(),
        award_date: z.date().optional(),
      })
    )
    .optional(),
});

const chatModel = new ChatOpenAI({
  model: process.env.OPEN_AI_MODEL,
  temperature: 0,
  apiKey: process.env.OPEN_AI_SECRET_KEY,
});
const embeddings = new OpenAIEmbeddings({
  openAIApiKey: process.env.OPEN_AI_SECRET_KEY, // Ensure you have this in your .env file
});


@Injectable()
export class LangchainService {
  constructor() {}

  async getTextFromPdf(buffer) {
    const loader = new PDFLoader(new Blob([buffer], { type: 'application/pdf' }), { splitPages : false });
    return await loader.load();
  }

  async resumeParser(text) {
    try{
      const structuredLlm = chatModel.withStructuredOutput(zodSchema, {
        name: "resume_extraction",
        includeRaw: false
      });
      return await structuredLlm.invoke(
        `Extract resume information from the following text:\n\n${text}`
      );
    }catch(error){
      Logger.log("Error From Langchain:", error);
      throw new InternalServerErrorException("Error From Langchain:", error);
    }
  }

  async getEmbeddings(text) {
    try {
      const vector = await embeddings.embedQuery(text);
      return vector;
    } catch (error) {
      console.error("Error generating embedding:", error);
    }
  }
}
