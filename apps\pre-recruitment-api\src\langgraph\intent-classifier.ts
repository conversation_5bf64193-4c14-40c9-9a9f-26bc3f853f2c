import { ChatOpenAI } from '@langchain/openai';
import { SystemMessage, HumanMessage } from 'langchain/schema';
import { QdrantClient } from '@qdrant/js-client-rest';

type SharedAgentState = {
  sessionId: string;
  userInput: string;
  userId: number;
  companyId: number;
  authHeader?: string;
  userPermissions?: string[];
  intent?: string;
  previousIntent?: string;
  conversationStage?: string;
  chatResponse?: string;
  formattedJobDetails?: any;
  formattedWorkflowDetails?: any;
  formattedAssessmentDetails?: any;
  isChatReset?: boolean;
  resetTimestamp?: string;
  workflowCreationContext?: {
    isFromWorkflow: boolean;
    assessmentType?: 'domain' | 'take-home' | 'live-coding';
    workflowSteps?: string[];
    workflowTitle?: string;
    returnToWorkflow?: boolean;
    createdAssessmentId?: number;
    createdAssessmentName?: string;
    needsConfiguration?: boolean;
    timeDuration?: number;
    deadline?: number;
    assessmentData?: any;
    returnProcessed?: boolean;
  };
};

function detectChatReset(userInput: string): boolean {
  const resetKeywords = [
    'reset chat', 'clear chat', 'restart chat', 'new chat', 'start over',
    'reset conversation', 'clear conversation', 'restart conversation',
    'exit', 'quit', 'end chat', 'start fresh', 'begin again',
    'reset session', 'clear session', 'new session'
  ];
  const input = userInput.toLowerCase().trim();
  return resetKeywords.some(keyword => input.includes(keyword)) ||
         /^(reset|clear|restart|exit|quit)$/i.test(input);
}

function convertToOpenAIRole(langchainRole: string): string {
  switch (langchainRole) {
    case 'human':
      return 'user';
    case 'ai':
      return 'assistant';
    case 'system':
      return 'system';
    case 'user':
      return 'user';
    case 'assistant':
      return 'assistant';
    default:
      // Default to user for unknown roles
      return 'user';
  }
}

async function storeResetMarker(qdrantClient: QdrantClient, sessionId: string, userId: number, companyId: number): Promise<string> {
  const resetTimestamp = new Date().toISOString();
  const resetId = `${sessionId}_reset_${Date.now()}`;

  try {
    const resetVector = new Array(1536).fill(0);
    resetVector[0] = 1;

    await qdrantClient.upsert('conversations', {
      wait: true,
      points: [{
        id: resetId,
        vector: resetVector,
        payload: {
          sessionId,
          userId,
          companyId,
          flowType: 'chat_reset',
          resetTimestamp,
          isResetMarker: true,
          messages: [{ role: 'system', content: `Chat reset at ${resetTimestamp}` }]
        }
      }]
    });

    return resetTimestamp;
  } catch (error) {
    console.error('[ChatReset] Error storing reset marker:', error);
    return resetTimestamp;
  }
}

export function createIntentClassifierNode() {
  const chatModel = new ChatOpenAI({
    modelName: process.env.OPEN_AI_MODEL || 'gpt-4o',
    temperature: 0,
    openAIApiKey: process.env.OPEN_AI_SECRET_KEY,
  });

  const systemPrompt = `You are an intelligent intent classifier for a recruitment system. Analyze conversation history and user input to determine the correct intent and routing.

CORE PRINCIPLES:
1. **Context Preservation**: Always consider the current conversation stage and flow
2. **Smart Routing**: Route between job, workflow, and assessment chatbots based on user intent
3. **Flow Continuity**: Maintain conversation flow unless user explicitly changes direction

AVAILABLE INTENTS:
- job: Job creation, editing, publishing, workflow assignment within jobs
- workflow: Workflow creation, editing, assessment integration
- assessment: Assessment creation (domain, take-home, live-coding), submission
- reset: Chat reset/restart

ROUTING LOGIC:
- **New Intent**: Clear creation keywords for different domain = route to new chatbot
- **Continuation**: Configuration, confirmations, modifications = stay in current chatbot
- **Cross-Integration**: Assessment creation within workflow = temporary route to assessment, then back
- **Workflow Assignment**: Within job context = stay in job chatbot

RESPONSE FORMAT - ALWAYS return valid JSON only:
{
  "intent": "job",
  "isNewIntent": false,
  "isChatReset": false,
  "reasoning": "Brief explanation"
}

IMPORTANT:
- Return ONLY the JSON object, no additional text
- Use double quotes for all strings
- Use lowercase for intent values: "job", "workflow", "assessment", or "reset"
- Use boolean values: true or false (not strings)

CRITICAL ROUTING RULES:
1. **Stage-Based Routing**: 
   - collect_* stages → workflow
   - *_creation stages → respective chatbot
   - workflow_assignment → assessment (for assignment handling)

2. **Context-Aware Decisions**:
   - "new/existing/ai" in assessment context → assessment
   - Workflow names after "available workflows" → job (for assignment)
   - "yes" after "submit assessment?" → assessment

3. **Flow Preservation**:
   - Assessment creation from workflow → assessment (temporary)
   - Assessment assignment back to workflow → workflow
   - Job workflow assignment → job

Analyze the conversation and route intelligently based on context, not keywords alone.`;

  return async (state: SharedAgentState): Promise<SharedAgentState> => {
    try {
      console.log('[IntentClassifier] Input:', state.userInput, 'Stage:', state.conversationStage, 'Intent:', state.intent);
      
      // Quick chat reset check
      if (detectChatReset(state.userInput)) {
        const qdrantClient = new QdrantClient({
          url: process.env.QDRANT_DATABASE_URL || 'http://localhost:6333',
          apiKey: process.env.QDRANT_API_KEY,
        });
        const resetTimestamp = await storeResetMarker(qdrantClient, state.sessionId, state.userId, state.companyId);
        return {
          ...state,
          intent: 'reset',
          previousIntent: undefined,
          conversationStage: 'initial',
          isChatReset: true,
          resetTimestamp,
          chatResponse: 'Chat has been reset. How can I help you today?',
        };
      }

      // Get conversation history
      let conversationHistory: { role: string; content: string }[] = [];
      try {
        const qdrantClient = new QdrantClient({
          url: process.env.QDRANT_DATABASE_URL || 'http://localhost:6333',
          apiKey: process.env.QDRANT_API_KEY,
        });

        const qdrantResponse = await qdrantClient.scroll('conversations', {
          filter: { must: [{ key: 'sessionId', match: { value: state.sessionId } }] },
          limit: 20,
        });

        if (qdrantResponse.points?.length > 0) {
          const payload = qdrantResponse.points[0].payload as any;
          if (payload?.messages) {
            // Convert LangChain message roles to OpenAI-compatible roles
            conversationHistory = payload.messages.slice(-10).map((msg: any) => ({
              role: convertToOpenAIRole(msg.role),
              content: msg.content
            }));
          }
        }
      } catch (error) {
        console.error('[IntentClassifier] Qdrant error:', error.message);
        console.warn('[IntentClassifier] Qdrant failed, continuing without history');
      }

      conversationHistory.push({ role: 'user', content: state.userInput });

      // Build context for LLM
      const contextSummary = `
CURRENT STATE:
- Intent: ${state.intent || 'none'}
- Stage: ${state.conversationStage || 'initial'}
- User Input: "${state.userInput}"

RECENT CONVERSATION:
${conversationHistory.slice(-5).map((msg, idx) => `${idx + 1}. ${msg.role}: ${msg.content}`).join('\n')}

Determine the correct intent routing based on context and user input.`;

      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(contextSummary),
      ];

      const llmResponse = await chatModel.invoke(messages);
      const content = llmResponse.content;

      if (typeof content === 'string') {
        try {
          // Simple JSON parsing - try direct parse first
          let parsed;
          try {
            parsed = JSON.parse(content.trim());
          } catch (directParseError) {
            // If direct parse fails, try to extract JSON from markdown
            const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
            if (jsonMatch) {
              parsed = JSON.parse(jsonMatch[1]);
            } else {
              // Try to find JSON object boundaries
              const jsonStart = content.indexOf('{');
              const jsonEnd = content.lastIndexOf('}');
              if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
                const jsonContent = content.substring(jsonStart, jsonEnd + 1);
                parsed = JSON.parse(jsonContent);
              } else {
                throw directParseError;
              }
            }
          }

          // Handle chat reset
          if (parsed.isChatReset === true || parsed.intent === 'reset') {
            const resetQdrantClient = new QdrantClient({
              url: process.env.QDRANT_DATABASE_URL || 'http://localhost:6333',
              apiKey: process.env.QDRANT_API_KEY,
            });
            const resetTimestamp = await storeResetMarker(resetQdrantClient, state.sessionId, state.userId, state.companyId);
            return {
              ...state,
              intent: 'reset',
              previousIntent: undefined,
              conversationStage: 'initial',
              isChatReset: true,
              resetTimestamp,
              chatResponse: 'Chat has been reset. How can I help you today?',
            };
          }

          // Smart routing based on LLM analysis and current context
          const targetIntent = parsed.intent?.toLowerCase() || 'job';
          const isNewIntent = parsed.isNewIntent === true;

          // Stage-based routing rules (minimal hardcoded logic)
          if (state.conversationStage?.startsWith('collect_')) {
            return { ...state, intent: 'workflow', conversationStage: state.conversationStage };
          }
          
          if (state.conversationStage === 'workflow_assignment') {
            return { ...state, intent: 'assessment', conversationStage: 'workflow_assignment' };
          }

          // Context preservation for ongoing flows
          if (!isNewIntent && state.conversationStage && state.intent) {
            const currentIntent = state.intent;
            const currentStage = state.conversationStage;
            
            // Keep in current chatbot unless explicitly changing
            if (currentStage.includes(currentIntent)) {
              return { ...state, intent: currentIntent, conversationStage: currentStage };
            }
          }

          // Route to target intent with appropriate stage
          let targetStage = 'initial';
          if (targetIntent === 'job') targetStage = 'job_creation';
          else if (targetIntent === 'workflow') targetStage = 'workflow_creation';
          else if (targetIntent === 'assessment') targetStage = 'assessment_creation';
          
          return {
            ...state,
            intent: targetIntent,
            previousIntent: targetIntent,
            conversationStage: targetStage,
          };

        } catch (err) {
          console.warn('[IntentClassifier] LLM parsing failed:', err.message);
          console.warn('[IntentClassifier] Raw LLM response:', content);
          console.warn('[IntentClassifier] Using fallback logic');
          return fallbackRouting(state, conversationHistory);
        }
      }
    } catch (error) {
      console.error('[IntentClassifier] Error:', error);
      return fallbackRouting(state, []);
    }
  };
}

// Simple fallback routing when LLM fails
function fallbackRouting(state: SharedAgentState, conversationHistory: any[]): SharedAgentState {
  const input = state.userInput.toLowerCase();
  
  // Stage-based routing
  if (state.conversationStage?.startsWith('collect_')) {
    return { ...state, intent: 'workflow', conversationStage: state.conversationStage };
  }
  
  if (state.conversationStage === 'workflow_assignment') {
    return { ...state, intent: 'assessment', conversationStage: 'workflow_assignment' };
  }
  
  // Context preservation
  if (state.conversationStage?.includes('assessment')) {
    return { ...state, intent: 'assessment', conversationStage: state.conversationStage };
  }
  
  if (state.conversationStage?.includes('workflow')) {
    return { ...state, intent: 'workflow', conversationStage: state.conversationStage };
  }
  
  if (state.conversationStage?.includes('job')) {
    return { ...state, intent: 'job', conversationStage: state.conversationStage };
  }
  
  // Keyword-based routing
  if (input.includes('workflow')) {
    return { ...state, intent: 'workflow', conversationStage: 'workflow_creation' };
  }
  
  if (input.includes('assessment')) {
    return { ...state, intent: 'assessment', conversationStage: 'assessment_creation' };
  }
  
  // Default to job
  return { ...state, intent: 'job', conversationStage: 'job_creation' };
}
