import { JobCreationChatbotService, WorkflowChatbotService } from '@microservices/recruitment-db';
import axios from 'axios';
import { createIntentClassifierNode } from './intent-classifier';

// Permission checking utility function
function checkUserPermissions(userPermissions: string[] = [], requiredPermissions: string[]): boolean {

  if (!userPermissions || userPermissions.length === 0) {
    return false;
  }

  // Check if user has any of the required permissions (OR logic)
  const hasPermission = requiredPermissions.some(permission => userPermissions.includes(permission));

  return hasPermission;
}

type SharedAgentState = {
  sessionId: string;
  userInput: string;
  userId: number;
  companyId: number;
  authHeader?: string;
  userPermissions?: string[];
  intent?: string;
  previousIntent?: string;
  conversationStage?: string;
  chatResponse?: string;
  formattedJobDetails?: any;
  formattedWorkflowDetails?: any;
  formattedAssessmentDetails?: any;
  isChatReset?: boolean;
  resetTimestamp?: string;

  workflowCreationContext?: {
    isFromWorkflow: boolean;
    isFromJobCreation?: boolean;
    jobId?: number;
    jobTitle?: string;
    assessmentType?: 'domain' | 'take-home' | 'live-coding';
    workflowSteps?: string[];
    workflowTitle?: string;
    returnToWorkflow?: boolean;
    returnToJobCreation?: boolean;
    createdAssessmentId?: number;
    createdAssessmentName?: string;
    needsConfiguration?: boolean;
    timeDuration?: number;
    deadline?: number;
    assessmentData?: any;
    returnProcessed?: boolean;
    createdWorkflowId?: number;
    createdWorkflowTitle?: string;
  };
};

// Helper function to handle assessment configuration collection
async function handleAssessmentConfiguration(
  state: SharedAgentState,
  workflowService: WorkflowChatbotService
): Promise<SharedAgentState> {
  const userInput = state.userInput.trim();
  const context = state.workflowCreationContext;

  if (!context) {
    return {
      ...state,
      chatResponse: 'Error: Missing workflow context. Please start over.',
      conversationStage: 'workflow_creation',
    };
  }

  try {
    if (state.conversationStage === 'collect_live_duration') {
      const duration = parseInt(userInput, 10);
      if (isNaN(duration) || duration < 1 || duration > 8) {
        return {
          ...state,
          chatResponse: 'Please enter a valid duration between 1 and 8 hours.',
        };
      }

      // Prepare assessment data for workflow creation
      const assessmentData = prepareAssessmentDataForWorkflow(state, duration, null);

      return {
        ...state,
        chatResponse: `✅ Perfect! Live coding assessment "${context.createdAssessmentName}" has been configured with ${duration} hour(s) duration and added to your workflow.\n\n🔄 Continuing with workflow creation. Would you like to continue?`,
        conversationStage: 'workflow_creation',
        workflowCreationContext: {
          ...context,
          timeDuration: duration,
          needsConfiguration: false,
          assessmentData: assessmentData,
        },
      };
    }

    if (state.conversationStage === 'collect_domain_deadline') {
      const deadline = parseInt(userInput, 10);
      if (isNaN(deadline) || deadline < 1 || deadline > 168) { // Max 1 week
        return {
          ...state,
          chatResponse: 'Please enter a valid deadline between 1 and 168 hours (1 week).',
        };
      }

      // Prepare assessment data for workflow creation
      const assessmentData = prepareAssessmentDataForWorkflow(state, null, deadline);

      return {
        ...state,
        chatResponse: `✅ Perfect! Domain assessment "${context.createdAssessmentName}" has been configured with ${deadline} hour(s) deadline and added to your workflow.\n\n🔄 Continuing with workflow creation. Would you like to continue?`,
        conversationStage: 'workflow_creation',
        workflowCreationContext: {
          ...context,
          deadline: deadline,
          needsConfiguration: false,
          assessmentData: assessmentData,
        },
      };
    }

    if (state.conversationStage === 'collect_takehome_deadline') {
      const deadlineDays = parseInt(userInput, 10);
      if (isNaN(deadlineDays) || deadlineDays < 1 || deadlineDays > 7) {
        return {
          ...state,
          chatResponse: 'Please enter a valid deadline between 1 and 7 days.',
        };
      }

      return {
        ...state,
        chatResponse: `Great! Deadline set to ${deadlineDays} day(s). Now, please specify the time duration for this take-home assessment in hours (e.g., 2, 4, 6):`,
        conversationStage: 'collect_takehome_duration',
        workflowCreationContext: {
          ...context,
          deadline: deadlineDays * 24, // Convert days to hours
        },
      };
    }

    if (state.conversationStage === 'collect_takehome_duration') {
      const duration = parseInt(userInput, 10);
      if (isNaN(duration) || duration < 1 || duration > 24) {
        return {
          ...state,
          chatResponse: 'Please enter a valid duration between 1 and 24 hours.',
        };
      }

      // Prepare assessment data for workflow creation
      const assessmentData = prepareAssessmentDataForWorkflow(state, duration, context.deadline);

      return {
        ...state,
        chatResponse: `Perfect! Assessment "${context.createdAssessmentName}" has been configured with ${context.deadline! / 24} day(s) deadline and ${duration} hour(s) duration and added to your workflow.\n\n🔄 Continuing with workflow creation. What other steps would you like to include?`,
        conversationStage: 'workflow_creation',
        workflowCreationContext: {
          ...context,
          timeDuration: duration,
          needsConfiguration: false,
          assessmentData: assessmentData,
        },
      };
    }

  } catch (error) {
    console.error('[handleAssessmentConfiguration] Error:', error);
    return {
      ...state,
      chatResponse: 'Something went wrong while configuring the assessment. Please try again.',
      conversationStage: 'workflow_creation',
    };
  }

  return state;
}

// Helper function to prepare assessment data for workflow creation
function prepareAssessmentDataForWorkflow(
  state: SharedAgentState,
  duration: number | null,
  deadline: number | null
): any {
  const context = state.workflowCreationContext;
  if (!context) return {};

  const assessmentData: any = {};

  if (context.assessmentType === 'live-coding') {
    assessmentData.liveCodingId = context.createdAssessmentId;
    assessmentData.liveCodingTime = duration;
    assessmentData.liveCodingQuestionType = 'live-task';
  } else if (context.assessmentType === 'domain') {
    assessmentData.domainId = context.createdAssessmentId;
    assessmentData.domainDeadline = deadline;
  } else if (context.assessmentType === 'take-home') {
    assessmentData.takeHomeTaskId = context.createdAssessmentId;
    assessmentData.takeHomeTaskDeadline = deadline;
    assessmentData.takeHomeTaskTime = duration;
    assessmentData.takeHomeTaskQuestionType = 'take-home-task';
  }

  return assessmentData;
}

export function createJobCreationNode(jobService: JobCreationChatbotService) {
  return async (state: SharedAgentState): Promise<SharedAgentState> => {
    try {
      // Check if user is trying to create a job and validate permissions
      const isJobCreationAttempt = (
        state.intent === 'job' ||
        state.userInput.toLowerCase().includes('create job') ||
        state.userInput.toLowerCase().includes('post job') ||
        state.userInput.toLowerCase().includes('new job') ||
        state.userInput.toLowerCase().includes('go lang') ||
        state.userInput.toLowerCase().includes('golang') ||
        state.userInput.toLowerCase().includes('developer') ||
        state.conversationStage === 'job_creation' ||
        (state.conversationStage === 'initial' && state.intent === 'job')
      );

      if (isJobCreationAttempt && !checkUserPermissions(state.userPermissions, ['job-post:add'])) {
        return {
          ...state,
          chatResponse: '**Permission Denied**: You do not have the required permissions to create jobs. Please contact your administrator to grant you the "job-post:add" permission.',
          conversationStage: 'permission_denied',
        };
      }

      const requestBody = {
        sessionId: state.sessionId,
        userInput: state.userInput,
        // Pass workflow creation data if returning from workflow creation
        ...(state.workflowCreationContext?.returnToJobCreation && state.workflowCreationContext?.createdWorkflowId && {
          workflowCreated: {
            workflowId: state.workflowCreationContext.createdWorkflowId,
            workflowTitle: state.workflowCreationContext.createdWorkflowTitle
          }
        })
      };

      const result = await jobService.startConversation(
        requestBody,
        state.companyId,
        state.userId
      );
    
      let conversationStage = 'job_creation';

      // Check for workflow creation transfer
      const isWorkflowCreationTransfer = result.chatResponse?.includes('I\'ll help you create a new workflow') ||
                                        result.chatResponse?.includes('transferred to workflow creation') ||
                                        result.chatResponse?.includes('create a new workflow') ||
                                        result.chatResponse?.includes('workflow creation system');

      if (isWorkflowCreationTransfer) {
        return {
          ...state,
          chatResponse: result.chatResponse,
          formattedJobDetails: result.formattedJobDetails,
          conversationStage: 'workflow_creation',
          intent: 'workflow',
          workflowCreationContext: {
            isFromJobCreation: true,
            isFromWorkflow: false,
            returnToJobCreation: true,
            jobId: result.formattedJobDetails?.jobId,
            jobTitle: result.formattedJobDetails?.title || 'Current Job',
          },
        };
      }

      if (result.chatResponse?.includes('Here are your available workflows:') ||
          result.chatResponse?.includes('Which workflow would you like to assign') ||
          result.chatResponse?.includes('available workflows for assignment') ||
          result.chatResponse?.includes('workflow(s) available for assignment') ||
          result.chatResponse?.includes('just type the workflow name')) {
        conversationStage = 'workflow_selection';
      }
      else if (result.chatResponse?.includes('has been assigned to the job') ||
               result.chatResponse?.includes('workflow assigned successfully') ||
               result.chatResponse?.includes('Workflow assignment completed')) {
        conversationStage = 'job_creation';
      }
      else if (result.chatResponse?.includes('Workflow') &&
               (result.chatResponse?.includes('not found') ||
                result.chatResponse?.includes('Please check'))) {
        conversationStage = 'workflow_selection';
      }
      else {
        conversationStage = state.conversationStage === 'workflow_selection' ? 'workflow_selection' : 'job_creation';
      }

      return {
        ...state,
        chatResponse: result.chatResponse,
        formattedJobDetails: result.formattedJobDetails,
        conversationStage,
      };
    } catch (error) {
      console.error('Error in job-agent node:', error);
      return {
        ...state,
        chatResponse: 'Something went wrong while processing your request.',
        formattedJobDetails: null,
        conversationStage: 'initial',
      };
    }
  };
}

export function createWorkflowCreationNode(workflowService: WorkflowChatbotService) {
  return async (state: SharedAgentState): Promise<SharedAgentState> => {
    try {
      // Check if user is trying to create a workflow and validate permissions
      const isWorkflowCreationAttempt = (
        state.intent === 'workflow' ||
        state.userInput.toLowerCase().includes('create workflow') ||
        state.userInput.toLowerCase().includes('new workflow') ||
        state.conversationStage === 'workflow_creation' ||
        state.conversationStage?.startsWith('collect_') ||
        (state.conversationStage === 'initial' && state.intent === 'workflow')
      );

      if (isWorkflowCreationAttempt && !checkUserPermissions(state.userPermissions, ['job-post:add', 'job-post:edit'])) {
        return {
          ...state,
          chatResponse: '**Permission Denied**: You do not have the required permissions to create workflows. Please contact your administrator to grant you either "job-post:add" or "job-post:edit" permission.',
          conversationStage: 'permission_denied',
        };
      }

      // Handle assessment configuration collection stages
      if (state.conversationStage?.startsWith('collect_')) {
        return handleAssessmentConfiguration(state, workflowService);
      }

      // Prepare the request body with assessment data if available
      const requestBody: any = {
        sessionId: state.sessionId,
        userInput: state.userInput
      };

      // Add assessment data to the request if available
      if (state.workflowCreationContext?.assessmentData) {
        requestBody.assessmentData = state.workflowCreationContext.assessmentData;
      }

      // Add context for workflow continuation after assessment configuration
      const isPostAssessmentContinuation = (
        state.conversationStage === 'workflow_creation' &&
        ['yes', 'y', 'continue', 'proceed'].includes(state.userInput.toLowerCase().trim()) &&
        state.workflowCreationContext?.needsConfiguration === false
      );

      if (isPostAssessmentContinuation) {
        requestBody.contextHint = 'WORKFLOW_CONTINUATION_AFTER_ASSESSMENT';
        requestBody.userInput = `Continue with workflow creation. User confirmed to proceed after assessment configuration.`;
      }

      // Add context for workflow assignment completion
      const isWorkflowAssignmentReturn = (
        state.conversationStage === 'workflow_creation' &&
        (state.chatResponse?.includes('assigned to your workflow') ||
         state.chatResponse?.includes('Returning to workflow creation')) &&
        state.workflowCreationContext?.assessmentData &&
        !state.workflowCreationContext?.returnProcessed
      );

      // Check if this is a workflow name input after assignment return
      const isWorkflowNameAfterAssignment = (
        state.conversationStage === 'workflow_creation' &&
        state.workflowCreationContext?.returnProcessed &&
        state.userInput &&
        !state.userInput.toLowerCase().includes('continue') &&
        !state.userInput.toLowerCase().includes('assessment')
      );

      if (isWorkflowAssignmentReturn) {
        requestBody.contextHint = 'WORKFLOW_RETURN_FROM_ASSIGNMENT';
        requestBody.userInput = `Continue with workflow creation. Assessment has been assigned to workflow and assessment data is available.`;

        // Mark that we've processed the return to avoid overriding subsequent user inputs
        if (state.workflowCreationContext) {
          state.workflowCreationContext.returnProcessed = true;
        }
      } else if (isWorkflowNameAfterAssignment) {
        // This is the workflow name input after assessment assignment
        requestBody.contextHint = 'WORKFLOW_NAME_AFTER_ASSIGNMENT';
        // Keep the original user input (workflow name)
        requestBody.userInput = state.userInput;
      }

      const result = await workflowService.startConversation(
        requestBody,
        state.companyId,
        state.userId,
        state.authHeader
      ) as { chatResponse: string; formattedWorkflowDetails: any };

      // Check for workflow creation completion and return to job creation
      const isWorkflowCompleted = result.chatResponse?.includes('workflow has been created successfully') ||
                                 result.chatResponse?.includes('Workflow created successfully') ||
                                 result.formattedWorkflowDetails?.id;

      if (isWorkflowCompleted && state.workflowCreationContext?.isFromJobCreation) {
        const workflowId = result.formattedWorkflowDetails?.id;
        const workflowTitle = result.formattedWorkflowDetails?.title || 'New Workflow';

        return {
          ...state,
          chatResponse: `✅ Great! Workflow "${workflowTitle}" has been created successfully.\n\n🔄 Now I'll assign this workflow to your job and publish it.`,
          formattedWorkflowDetails: result.formattedWorkflowDetails,
          conversationStage: 'job_creation',
          intent: 'job',
          workflowCreationContext: {
            ...state.workflowCreationContext,
            createdWorkflowId: workflowId,
            createdWorkflowTitle: workflowTitle,
            returnToJobCreation: true,
          },
        };
      }

      // Enhanced detection for assessment creation transfer
      const isAssessmentTransfer = result.chatResponse?.includes('You\'ll be transferred to the assessment creation system') ||
                                   result.chatResponse?.includes('I\'ll help you create a new') ||
                                   result.chatResponse?.includes('assessment creation system') ||
                                   result.chatResponse?.includes('Please specify \'') ||
                                   result.chatResponse?.includes('to begin creation') ||
                                   result.chatResponse?.includes('create a domain assessment') ||
                                   result.chatResponse?.includes('create a take-home assessment') ||
                                   result.chatResponse?.includes('create a live coding assessment') ||
                                   result.chatResponse?.includes('Let\'s create your assessment') ||
                                   result.chatResponse?.includes('assessment creation process') ||
                                   result.chatResponse?.includes('I\'ll help you create a live coding assessment') ||
                                   result.chatResponse?.includes('I\'ll help you create a take-home assessment') ||
                                   result.chatResponse?.includes('I\'ll help you create a domain assessment');

      if (isAssessmentTransfer) {
        let assessmentType: 'domain' | 'take-home' | 'live-coding' = 'domain';

        // More robust assessment type detection
        if (result.chatResponse.includes('take-home') || result.chatResponse.includes('take home')) {
          assessmentType = 'take-home';
        } else if (result.chatResponse.includes('live coding') || result.chatResponse.includes('live-coding') ||
                   result.chatResponse.includes('live task') || result.chatResponse.includes('coding assessment') ||
                   result.chatResponse.includes('Live Task/Coding Assessment')) {
          assessmentType = 'live-coding';
        } else if (result.chatResponse.includes('domain') || result.chatResponse.includes('knowledge') ||
                   result.chatResponse.includes('Functional/Domain Assessment')) {
          assessmentType = 'domain';
        }


        return {
          ...state,
          chatResponse: result.chatResponse,
          formattedWorkflowDetails: result.formattedWorkflowDetails,
          conversationStage: 'assessment_creation',
          intent: 'assessment',
          workflowCreationContext: {
            isFromWorkflow: true,
            assessmentType,
            returnToWorkflow: true,
            workflowTitle: result.formattedWorkflowDetails?.title || 'Current Workflow',
            workflowSteps: result.formattedWorkflowDetails?.steps || [],
          },
        };
      }

      return {
        ...state,
        chatResponse: result.chatResponse,
        formattedWorkflowDetails: result.formattedWorkflowDetails,
        conversationStage: state.conversationStage === 'workflow_selection' ? 'job_creation' : 'workflow_creation',
      };
    } catch (error) {
      console.error('Error in workflow-agent node:', error);
      return {
        ...state,
        chatResponse: 'Something went wrong while processing your request.',
        formattedWorkflowDetails: null,
        conversationStage: 'initial',
      };
    }
  };
}

export function createAssessmentNode(apiBaseUrl?: string) {
  return async (state: SharedAgentState): Promise<SharedAgentState> => {
    try {
      // Check if user is trying to create an assessment and validate permissions
      const isAssessmentCreationAttempt = (
        state.intent === 'assessment' ||
        state.userInput.toLowerCase().includes('create assessment') ||
        state.userInput.toLowerCase().includes('new assessment') ||
        state.userInput.toLowerCase().includes('live coding') ||
        state.userInput.toLowerCase().includes('take-home') ||
        state.userInput.toLowerCase().includes('domain assessment') ||
        state.conversationStage === 'assessment_creation' ||
        (state.conversationStage === 'initial' && state.intent === 'assessment')
      );

      if (isAssessmentCreationAttempt && !checkUserPermissions(state.userPermissions, ['assessment:edit'])) {
        return {
          ...state,
          chatResponse: '**Permission Denied**: You do not have the required permissions to create assessments. Please contact your administrator to grant you the "assessment:edit" permission.',
          conversationStage: 'permission_denied',
        };
      }

      const assessmentApiUrl = process.env.ASSESSMENT_API_URI || 'http://localhost:5481';
      const apiUrl = `${assessmentApiUrl}/api/assessment-chatbot/converse`;

      const headers = {
        'Content-Type': 'application/json',
        Authorization: state.authHeader,
      };

      // Map our workflowCreationContext to the format expected by assessment service
      const workflowContext = state.workflowCreationContext ? {
        isFromWorkflow: state.workflowCreationContext.isFromWorkflow,
        assessmentType: state.workflowCreationContext.assessmentType,
        workflowSteps: state.workflowCreationContext.workflowSteps,
        workflowTitle: state.workflowCreationContext.workflowTitle,
        returnToWorkflow: state.workflowCreationContext.returnToWorkflow,
      } : undefined;

      const body = {
        sessionId: state.sessionId,
        userInput: state.userInput,
        companyId: state.companyId,
        userId: state.userId,
        workflowContext: workflowContext,
      };


      const response = await axios.post(apiUrl, body, { headers });

      const result = response.data as {
        response: string;
        formattedAssessmentDetails: any;
        assessmentCreated?: boolean;
        assessmentId?: number;
        assessmentName?: string;
        data?: any;
        completed?: boolean;
        workflowAssignment?: any;
      };


      // Check for successful submission first
      if (result.response?.includes('Assessment submitted successfully')) {
        return {
          ...state,
          chatResponse: result.response,
          formattedAssessmentDetails: result.formattedAssessmentDetails,
          conversationStage: 'assessment_completed',
        };
      }

      const isAssessmentCompleted = result.assessmentCreated ||
                                   result.response?.includes('assessment has been created') ||
                                   result.response?.includes('successfully created') ||
                                   result.response?.includes('Assessment submitted successfully');

      const isWorkflowAssignmentResponse = result.response?.includes('Would you like to assign this assessment to your workflow?') ||
                                          result.response?.includes('assign this assessment to the workflow') ||
                                          result.response?.includes('add this assessment to your workflow');

      // Check if this is a workflow context assessment that should return directly
      const isFromWorkflowContext = state.workflowCreationContext?.isFromWorkflow &&
                                   state.workflowCreationContext?.returnToWorkflow &&
                                   isAssessmentCompleted;


      if (isWorkflowAssignmentResponse && !isFromWorkflowContext) {
        return {
          ...state,
          chatResponse: result.response,
          formattedAssessmentDetails: result.formattedAssessmentDetails,
          conversationStage: 'workflow_assignment',
          intent: 'assessment', // Keep intent as assessment to handle the assignment response
        };
      }

      const isWorkflowAssignmentComplete = (result.response?.includes('assigned to your workflow') ||
                                          result.response?.includes('Returning to workflow creation') ||
                                          result.response?.includes('not assigned to the workflow') ||
                                          result.response?.includes('assessment has been assigned')) &&
                                          (result.completed || result.assessmentCreated);

      if (isWorkflowAssignmentComplete) {
        // Extract assignment data from the result
        const assignmentData = result.data?.workflowAssignment;

        // Prepare assessment data for workflow creation if assignment was successful
        let assessmentData = null;
        if (assignmentData?.assigned && assignmentData?.assessmentId) {
          const assessmentType = assignmentData.assessmentType;
          assessmentData = {};

          if (assessmentType === 'live-coding') {
            assessmentData.liveCodingId = assignmentData.assessmentId;
            assessmentData.liveCodingQuestionType = 'live-task';
          } else if (assessmentType === 'domain') {
            assessmentData.domainId = assignmentData.assessmentId;
          } else if (assessmentType === 'take-home') {
            assessmentData.takeHomeTaskId = assignmentData.assessmentId;
            assessmentData.takeHomeTaskQuestionType = 'take-home-task';
          }
        }

        // Set workflow creation context for proper handling
        return {
          ...state,
          chatResponse: result.response,
          formattedAssessmentDetails: result.formattedAssessmentDetails,
          conversationStage: 'workflow_creation',
          intent: 'workflow',
          workflowCreationContext: {
            ...state.workflowCreationContext,
            assessmentData: assessmentData,
            returnToWorkflow: false,
            needsConfiguration: false,
          },
        };
      }

      // Enhanced logic for returning to workflow after assessment creation
      if (isFromWorkflowContext) {

        const workflowTitle = state.workflowCreationContext.workflowTitle || 'your workflow';
        const assessmentName = result.assessmentName || result.data?.name || 'New Assessment';
        const assessmentType = state.workflowCreationContext.assessmentType;

        // Try to extract assessment ID from different possible locations
        let assessmentId = result.assessmentId || result.data?.id;
        if (!assessmentId && result.data?.jsonOutput) {
          // For live coding, the ID might be in the created record
        }


        // Determine what additional parameters to collect based on assessment type
        let nextPrompt = '';
        let nextStage = 'workflow_creation';

        if (assessmentType === 'live-coding') {
          nextPrompt = `✅ Great! The live coding assessment "${assessmentName}" has been created successfully.\n\n⏱️ Now, please specify the time duration for this live coding assessment in hours (e.g., 1, 2, 3):`;
          nextStage = 'collect_live_duration';
        } else if (assessmentType === 'domain') {
          nextPrompt = `✅ Great! The domain assessment "${assessmentName}" has been created successfully.\n\n📅 Now, please specify the deadline for this domain assessment in hours (e.g., 24, 48, 72):`;
          nextStage = 'collect_domain_deadline';
        } else if (assessmentType === 'take-home') {
          nextPrompt = `✅ Great! The take-home assessment "${assessmentName}" has been created successfully.\n\n📅 Now, please specify the deadline for this take-home assessment in days (e.g., 1, 2, 3):`;
          nextStage = 'collect_takehome_deadline';
        }


        return {
          ...state,
          chatResponse: nextPrompt,
          formattedAssessmentDetails: result.formattedAssessmentDetails,
          conversationStage: nextStage,
          intent: 'workflow',
          workflowCreationContext: {
            ...state.workflowCreationContext,
            returnToWorkflow: false,
            createdAssessmentId: assessmentId,
            createdAssessmentName: assessmentName,
            needsConfiguration: true,
          },
        };
      }

      // Determine conversation stage based on assessment response
      let conversationStage = 'assessment_creation';

      // Check if assessment is ready for submission
      if (result.response?.includes('ready to be submitted') &&
          result.response?.includes('Would you like to submit this assessment?')) {
        conversationStage = 'assessment_submission';
      }

      return {
        ...state,
        chatResponse: result.response,
        formattedAssessmentDetails: result.formattedAssessmentDetails,
        conversationStage: conversationStage,
      };
    } catch (error) {
      console.error('Error in assessment-agent node:', error.response?.data || error);
      return {
        ...state,
        chatResponse: 'Something went wrong while processing your assessment request.',
        formattedAssessmentDetails: null,
        conversationStage: 'initial',
      };
    }
  };
}

// Export the intent classifier from the separate file
export { createIntentClassifierNode };